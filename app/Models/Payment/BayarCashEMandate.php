<?php

namespace App\Models\Payment;

use App\Traits\UUID;
use App\Models\User;
use App\Models\Company;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * BayarCash E-Mandate Enrollment Model
 * 
 * This model handles FPX Direct Debit e-mandate enrollments for subscription payments.
 * It tracks the enrollment process, status, and recurring payment schedules.
 */
class BayarCashEMandate extends Model
{
    use HasFactory, UUID, SoftDeletes;

    protected $table = 'bayarcash_emandate_enrollments';

    protected $fillable = [
        'user_id',
        'company_id',
        'enrollment_id',
        'order_number',
        'order_description',
        'portal_key',
        'amount',
        'payer_id_type',
        'payer_id',
        'payer_name',
        'payer_email',
        'payer_telephone_number',
        'frequency_mode',
        'application_reason',
        'effective_date',
        'expiry_date',
        'status',
        'status_code',
        'status_message',
        'enrollment_url',
        'return_url',
        'success_url',
        'failed_url',
        'callback_data',
        'metadata',
        'enrolled_at',
        'last_payment_at',
        'next_payment_at'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'amount' => 'float',
        'effective_date' => 'date',
        'expiry_date' => 'date',
        'enrolled_at' => 'datetime',
        'last_payment_at' => 'datetime',
        'next_payment_at' => 'datetime',
        'callback_data' => 'array',
        'metadata' => 'array',
    ];

    /**
     * E-mandate status constants (aligned with BayarCash SDK)
     */
    const STATUS_PENDING = 'pending';
    const STATUS_ACTIVE = 'active';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_EXPIRED = 'expired';
    const STATUS_NEW = 'new';
    const STATUS_WAITING_APPROVAL = 'waiting_approval';
    const STATUS_FAILED_BANK_VERIFICATION = 'failed_bank_verification';
    const STATUS_TERMINATED = 'terminated';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_ERROR = 'error';

    /**
     * BayarCash SDK status code constants
     */
    const SDK_STATUS_NEW = 0;
    const SDK_STATUS_WAITING_APPROVAL = 1;
    const SDK_STATUS_FAILED_BANK_VERIFICATION = 2;
    const SDK_STATUS_ACTIVE = 3;
    const SDK_STATUS_TERMINATED = 4;
    const SDK_STATUS_APPROVED = 5;
    const SDK_STATUS_REJECTED = 6;
    const SDK_STATUS_CANCELLED = 7;
    const SDK_STATUS_ERROR = 8;

    /**
     * Frequency mode constants (aligned with BayarCash SDK)
     */
    const FREQUENCY_DAILY = 'DL';
    const FREQUENCY_WEEKLY = 'WK';
    const FREQUENCY_MONTHLY = 'MT';
    const FREQUENCY_YEARLY = 'YR';

    /**
     * Payer ID type constants (aligned with BayarCash SDK)
     */
    const PAYER_ID_TYPE_NRIC = 1;
    const PAYER_ID_TYPE_OLD_IC = 2;
    const PAYER_ID_TYPE_PASSPORT = 3;
    const PAYER_ID_TYPE_BUSINESS_REGISTRATION = 4;
    const PAYER_ID_TYPE_OTHERS = 5;

    /**
     * Get the user that owns the e-mandate enrollment.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the company that owns the e-mandate enrollment.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Scope to get active e-mandates
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope to get pending e-mandates
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope to get e-mandates due for payment
     */
    public function scopeDueForPayment($query)
    {
        return $query->where('status', self::STATUS_ACTIVE)
                    ->where('next_payment_at', '<=', now());
    }

    /**
     * Check if the e-mandate is active
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Check if the e-mandate is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if the e-mandate has failed
     */
    public function hasFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * Check if the e-mandate is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    /**
     * Check if the e-mandate has expired
     */
    public function hasExpired(): bool
    {
        return $this->status === self::STATUS_EXPIRED || 
               ($this->expiry_date && $this->expiry_date->isPast());
    }

    /**
     * Mark the e-mandate as active
     */
    public function markAsActive(): void
    {
        $this->update([
            'status' => self::STATUS_ACTIVE,
            'enrolled_at' => now(),
            'next_payment_at' => $this->calculateNextPaymentDate()
        ]);
    }

    /**
     * Mark the e-mandate as failed
     */
    public function markAsFailed(string $message = null): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'status_message' => $message
        ]);
    }

    /**
     * Mark the e-mandate as cancelled
     */
    public function markAsCancelled(string $message = null): void
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
            'status_message' => $message
        ]);
    }

    /**
     * Update payment information after successful payment
     */
    public function updatePaymentInfo(): void
    {
        $this->update([
            'last_payment_at' => now(),
            'next_payment_at' => $this->calculateNextPaymentDate()
        ]);
    }

    /**
     * Calculate the next payment date based on frequency mode
     */
    public function calculateNextPaymentDate(): \Carbon\Carbon
    {
        $baseDate = $this->last_payment_at ?? $this->effective_date ?? now();
        
        switch ($this->frequency_mode) {
            // case self::FREQUENCY_WEEKLY:
            //     return $baseDate->addWeek();
            case self::FREQUENCY_MONTHLY:
                return $baseDate->addMonth();
            // case self::FREQUENCY_QUARTERLY:
            //     return $baseDate->addMonths(3);
            case self::FREQUENCY_YEARLY:
                return $baseDate->addYear();
            default:
                return $baseDate->addMonth(); // Default to monthly
        }
    }

    /**
     * Get frequency mode options (aligned with BayarCash SDK)
     */
    public static function getFrequencyModeOptions(): array
    {
        return [
            self::FREQUENCY_DAILY => 'Daily',
            self::FREQUENCY_WEEKLY => 'Weekly',
            self::FREQUENCY_MONTHLY => 'Monthly',
            self::FREQUENCY_YEARLY => 'Yearly'
        ];
    }

    /**
     * Get payer ID type options (aligned with BayarCash SDK)
     */
    public static function getPayerIdTypeOptions(): array
    {
        return [
            self::PAYER_ID_TYPE_NRIC => 'NRIC',
            self::PAYER_ID_TYPE_OLD_IC => 'Old IC',
            self::PAYER_ID_TYPE_PASSPORT => 'Passport',
            self::PAYER_ID_TYPE_BUSINESS_REGISTRATION => 'Business Registration',
            self::PAYER_ID_TYPE_OTHERS => 'Others'
        ];
    }

    /**
     * Map SDK status code to internal status
     */
    public static function mapSdkStatusToInternal(int $sdkStatus): string
    {
        switch ($sdkStatus) {
            case self::SDK_STATUS_NEW:
                return self::STATUS_NEW;
            case self::SDK_STATUS_WAITING_APPROVAL:
                return self::STATUS_WAITING_APPROVAL;
            case self::SDK_STATUS_FAILED_BANK_VERIFICATION:
                return self::STATUS_FAILED_BANK_VERIFICATION;
            case self::SDK_STATUS_ACTIVE:
                return self::STATUS_ACTIVE;
            case self::SDK_STATUS_TERMINATED:
                return self::STATUS_TERMINATED;
            case self::SDK_STATUS_APPROVED:
                return self::STATUS_APPROVED;
            case self::SDK_STATUS_REJECTED:
                return self::STATUS_REJECTED;
            case self::SDK_STATUS_CANCELLED:
                return self::STATUS_CANCELLED;
            case self::SDK_STATUS_ERROR:
                return self::STATUS_ERROR;
            default:
                return self::STATUS_PENDING; // Fallback
        }
    }

    /**
     * Get SDK status interpretation
     */
    public static function getSdkStatusInterpretation(int $sdkStatus): array
    {
        switch ($sdkStatus) {
            case self::SDK_STATUS_NEW:
                return [
                    'status' => 'New',
                    'description' => 'E-mandate enrollment is new/pending',
                    'is_success' => false,
                    'is_failure' => false,
                    'is_pending' => true
                ];
            case self::SDK_STATUS_WAITING_APPROVAL:
                return [
                    'status' => 'Waiting Approval',
                    'description' => 'E-mandate is waiting for approval',
                    'is_success' => false,
                    'is_failure' => false,
                    'is_pending' => true
                ];
            case self::SDK_STATUS_FAILED_BANK_VERIFICATION:
                return [
                    'status' => 'Failed Bank Verification',
                    'description' => 'Bank verification failed',
                    'is_success' => false,
                    'is_failure' => true,
                    'is_pending' => false
                ];
            case self::SDK_STATUS_ACTIVE:
                return [
                    'status' => 'Active',
                    'description' => 'E-mandate is active and ready for payments',
                    'is_success' => true,
                    'is_failure' => false,
                    'is_pending' => false
                ];
            case self::SDK_STATUS_TERMINATED:
                return [
                    'status' => 'Terminated',
                    'description' => 'E-mandate has been terminated',
                    'is_success' => false,
                    'is_failure' => true,
                    'is_pending' => false
                ];
            case self::SDK_STATUS_APPROVED:
                return [
                    'status' => 'Approved',
                    'description' => 'E-mandate has been approved',
                    'is_success' => true,
                    'is_failure' => false,
                    'is_pending' => false
                ];
            case self::SDK_STATUS_REJECTED:
                return [
                    'status' => 'Rejected',
                    'description' => 'E-mandate has been rejected',
                    'is_success' => false,
                    'is_failure' => true,
                    'is_pending' => false
                ];
            case self::SDK_STATUS_CANCELLED:
                return [
                    'status' => 'Cancelled',
                    'description' => 'E-mandate has been cancelled',
                    'is_success' => false,
                    'is_failure' => true,
                    'is_pending' => false
                ];
            case self::SDK_STATUS_ERROR:
                return [
                    'status' => 'Error',
                    'description' => 'An error occurred with the e-mandate',
                    'is_success' => false,
                    'is_failure' => true,
                    'is_pending' => false
                ];
            default:
                return [
                    'status' => 'Unknown',
                    'description' => 'Unknown status code',
                    'is_success' => false,
                    'is_failure' => false,
                    'is_pending' => true
                ];
        }
    }

    /**
     * Get status options
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_ACTIVE => 'Active',
            self::STATUS_FAILED => 'Failed',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_EXPIRED => 'Expired'
        ];
    }
}
