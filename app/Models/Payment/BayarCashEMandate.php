<?php

namespace App\Models\Payment;

use App\Traits\UUID;
use App\Models\User;
use App\Models\Company;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * BayarCash E-Mandate Enrollment Model
 * 
 * This model handles FPX Direct Debit e-mandate enrollments for subscription payments.
 * It tracks the enrollment process, status, and recurring payment schedules.
 */
class BayarCashEMandate extends Model
{
    use HasFactory, UUID, SoftDeletes;

    protected $table = 'bayarcash_emandate_enrollments';

    protected $fillable = [
        'user_id',
        'company_id',
        'enrollment_id',
        'order_number',
        'portal_key',
        'amount',
        'payer_id_type',
        'payer_id',
        'payer_name',
        'payer_email',
        'payer_telephone_number',
        'frequency_mode',
        'application_reason',
        'effective_date',
        'expiry_date',
        'status',
        'status_code',
        'status_message',
        'enrollment_url',
        'return_url',
        'success_url',
        'failed_url',
        'callback_data',
        'metadata',
        'enrolled_at',
        'last_payment_at',
        'next_payment_at'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'amount' => 'float',
        'effective_date' => 'date',
        'expiry_date' => 'date',
        'enrolled_at' => 'datetime',
        'last_payment_at' => 'datetime',
        'next_payment_at' => 'datetime',
        'callback_data' => 'array',
        'metadata' => 'array',
    ];

    /**
     * E-mandate status constants
     */
    const STATUS_PENDING = 'pending';
    const STATUS_ACTIVE = 'active';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_EXPIRED = 'expired';

    /**
     * Frequency mode constants
     */
    const FREQUENCY_MONTHLY = 'MT';
    const FREQUENCY_YEARLY = 'YR';
    // const FREQUENCY_WEEKLY = 'WEEKLY';
    // const FREQUENCY_QUARTERLY = 'QUARTERLY';

    /**
     * Payer ID type constants
     */
    const PAYER_ID_TYPE_NRIC = '1';
    const PAYER_ID_TYPE_PASSPORT = '2';
    const PAYER_ID_TYPE_COMPANY = '3';

    /**
     * Get the user that owns the e-mandate enrollment.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the company that owns the e-mandate enrollment.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Scope to get active e-mandates
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope to get pending e-mandates
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope to get e-mandates due for payment
     */
    public function scopeDueForPayment($query)
    {
        return $query->where('status', self::STATUS_ACTIVE)
                    ->where('next_payment_at', '<=', now());
    }

    /**
     * Check if the e-mandate is active
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Check if the e-mandate is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if the e-mandate has failed
     */
    public function hasFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * Check if the e-mandate is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    /**
     * Check if the e-mandate has expired
     */
    public function hasExpired(): bool
    {
        return $this->status === self::STATUS_EXPIRED || 
               ($this->expiry_date && $this->expiry_date->isPast());
    }

    /**
     * Mark the e-mandate as active
     */
    public function markAsActive(): void
    {
        $this->update([
            'status' => self::STATUS_ACTIVE,
            'enrolled_at' => now(),
            'next_payment_at' => $this->calculateNextPaymentDate()
        ]);
    }

    /**
     * Mark the e-mandate as failed
     */
    public function markAsFailed(string $message = null): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'status_message' => $message
        ]);
    }

    /**
     * Mark the e-mandate as cancelled
     */
    public function markAsCancelled(string $message = null): void
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
            'status_message' => $message
        ]);
    }

    /**
     * Update payment information after successful payment
     */
    public function updatePaymentInfo(): void
    {
        $this->update([
            'last_payment_at' => now(),
            'next_payment_at' => $this->calculateNextPaymentDate()
        ]);
    }

    /**
     * Calculate the next payment date based on frequency mode
     */
    public function calculateNextPaymentDate(): \Carbon\Carbon
    {
        $baseDate = $this->last_payment_at ?? $this->effective_date ?? now();
        
        switch ($this->frequency_mode) {
            // case self::FREQUENCY_WEEKLY:
            //     return $baseDate->addWeek();
            case self::FREQUENCY_MONTHLY:
                return $baseDate->addMonth();
            // case self::FREQUENCY_QUARTERLY:
            //     return $baseDate->addMonths(3);
            case self::FREQUENCY_YEARLY:
                return $baseDate->addYear();
            default:
                return $baseDate->addMonth(); // Default to monthly
        }
    }

    /**
     * Get frequency mode options
     */
    public static function getFrequencyModeOptions(): array
    {
        return [
            // self::FREQUENCY_WEEKLY => 'Weekly',
            self::FREQUENCY_MONTHLY => 'MT',
            // self::FREQUENCY_QUARTERLY => 'Quarterly',
            self::FREQUENCY_YEARLY => 'YR'
        ];
    }

    /**
     * Get payer ID type options
     */
    public static function getPayerIdTypeOptions(): array
    {
        return [
            self::PAYER_ID_TYPE_NRIC => 'NRIC',
            self::PAYER_ID_TYPE_PASSPORT => 'Passport',
            self::PAYER_ID_TYPE_COMPANY => 'Company Registration'
        ];
    }

    /**
     * Get status options
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_ACTIVE => 'Active',
            self::STATUS_FAILED => 'Failed',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_EXPIRED => 'Expired'
        ];
    }
}
