<?php

namespace App\Services;

use GuzzleHttp\Client as HttpClient;
use Webimpian\BayarcashSdk\Bayarcash;

class CustomBayarCashSDK extends Bayarcash
{
    /**
     * @var string
     */
    protected $customBaseUrl;

    /**
     * Constructor
     *
     * @param string $token
     * @param string|null $customBaseUrl
     */
    public function __construct(string $token, ?string $customBaseUrl = null)
    {
        $this->customBaseUrl = $customBaseUrl;

        // Call parent constructor to properly initialize the SDK
        parent::__construct($token);

        // Override with our custom initialization if needed
        if ($customBaseUrl) {
            $this->initializeGuzzle();
        }
    }

    /**
     * Initialize or reinitialize the Guzzle HTTP client.
     *
     * @return void
     */
    private function initializeGuzzle()
    {
        // Log the base URI and token being used
        \Illuminate\Support\Facades\Log::debug('CustomBayarCashSDK initializing Guzzle', [
            'base_uri' => $this->customBaseUrl ?: $this->getBaseUri(),
            'token_length' => strlen($this->token),
            // 'token_prefix' => substr($this->token, 0, 20) . '...'
            'token_prefix' => $this->token
        ]);

        $this->guzzle = new HttpClient([
            'base_uri' => $this->customBaseUrl ?: $this->getBaseUri(),
            'http_errors' => false,
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
        ]);
    }

    /**
     * Override the getBaseUri method to use our custom base URL
     *
     * @return string
     */
    protected function getBaseUri()
    {
        if ($this->customBaseUrl) {
            return $this->customBaseUrl;
        }

        return parent::getBaseUri();
    }

    /**
     * Set a custom base URL
     *
     * @param string $url
     * @return $this
     */
    public function setBaseUrl(string $url)
    {
        $this->customBaseUrl = $url;
        $this->initializeGuzzle();
        return $this;
    }
}
