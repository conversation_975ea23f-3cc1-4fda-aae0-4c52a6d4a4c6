<?php

namespace App\Services;

use Exception;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Models\Payment\BayarCashEMandate;
use App\Services\CustomBayarCashSDK;

/**
 * BayarCash E-Mandate Service
 * 
 * This service handles FPX Direct Debit e-mandate enrollment and management.
 * It provides methods for creating enrollment intents, handling callbacks,
 * and managing recurring payment schedules.
 */
class BayarCashEMandateService
{
    /**
     * @var CustomBayarCashSDK
     */
    protected $bayarcash;

    /**
     * @var string
     */
    protected $apiSecretKey;

    /**
     * @var bool
     */
    protected $useSandbox;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->apiSecretKey = config('services.bayarcash.api_secret_key');
        $this->useSandbox = config('services.bayarcash.sandbox', true);

        // Get base URL based on environment
        $baseUrl = $this->useSandbox
            ? config('services.bayarcash.sandbox_base_url', 'https://api.console.bayarcash-sandbox.com/v3/')
            : config('services.bayarcash.base_url', 'https://api.console.bayar.cash/v3/');

        try {
            // Initialize the custom BayarCash SDK with the correct base URL
            $this->bayarcash = new CustomBayarCashSDK(
                config('services.bayarcash.api_token'),
                $baseUrl
            );

            // Set API version
            $this->bayarcash->setApiVersion(config('services.bayarcash.api_version', 'v3'));

            // Set environment
            if ($this->useSandbox) {
                $this->bayarcash->useSandbox();
            }

            Log::info('BayarCash E-Mandate service initialized successfully');
        } catch (Exception $e) {
            Log::error('Exception when initializing BayarCash E-Mandate service', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Create an e-mandate enrollment intent
     * 
     * @param array $data Enrollment data
     * @return array Result with success status and data
     */
    public function createEnrollmentIntent(array $data): array
    {
        try {
            // Log incoming data for debugging
            Log::debug('BayarCash e-mandate enrollment data received', [
                'data_keys' => array_keys($data),
                'payer_id_present' => isset($data['payer_id']),
                'payer_id_value' => $data['payer_id'] ?? 'NOT_SET',
                'payer_telephone_number' => $data['payer_telephone_number'] ?? 'NOT_SET'
            ]);

            // Validate required fields
            $this->validateEnrollmentData($data);

            // Generate unique order number if not provided (max 30 characters)
            if (!isset($data['order_number'])) {
                $uniqueId = substr(uniqid(), 0, 8); // Limit uniqid to 8 characters
                $data['order_number'] = 'EMANDATE_' . $uniqueId;
            }

            // Ensure order number doesn't exceed 30 characters
            if (strlen($data['order_number']) > 30) {
                $data['order_number'] = substr($data['order_number'], 0, 30);
            }

            // Generate order description if not provided
            if (!isset($data['order_description'])) {
                $data['order_description'] = $data['application_reason'] ?? 'E-Mandate Subscription';
            }

            // Format phone number to Malaysian format if needed
            $formattedPhone = $this->formatMalaysianPhoneNumber($data['payer_telephone_number']);

            // Prepare enrollment request data
            $requestData = [
                'portal_key' => $data['portal_key'] ?? config('services.bayarcash.portal_key'),
                'order_number' => $data['order_number'],
                'order_description' => $data['order_description'],
                'amount' => $data['amount'],
                'payer_id_type' => $data['payer_id_type'],
                'payer_id' => $data['payer_id'],
                'payer_name' => $data['payer_name'],
                'payer_email' => $data['payer_email'],
                'payer_telephone_number' => $formattedPhone,
                'frequency_mode' => $data['frequency_mode'],
                'application_reason' => $data['application_reason'],
                'effective_date' => $data['effective_date'],
                'return_url' => $data['return_url'] ?? route('payment.bayarcash-emandate.callback'),
                'success_url' => $data['success_url'] ?? route('payment.bayarcash-emandate.success'),
                'failed_url' => $data['failed_url'] ?? route('payment.bayarcash-emandate.failed'),
            ];

            // Add optional fields
            if (isset($data['expiry_date'])) {
                $requestData['expiry_date'] = $data['expiry_date'];
            }

            if (isset($data['metadata'])) {
                $requestData['metadata'] = $data['metadata'];
            }

            // Let the SDK handle checksum generation internally
            // Remove any existing checksum to avoid conflicts
            unset($requestData['checksum']);

            Log::debug('Letting SDK handle checksum generation internally');

            // Log the complete payload being sent to BayarCash API
            Log::debug('Complete BayarCash e-mandate payload', [
                'payload' => $requestData,
                'payload_json' => json_encode($requestData)
            ]);

            Log::info('Creating BayarCash e-mandate enrollment intent', [
                'order_number' => $requestData['order_number'],
                'order_description' => $requestData['order_description'],
                'portal_key' => $requestData['portal_key'],
                'amount' => $requestData['amount'],
                'frequency_mode' => $requestData['frequency_mode'],
                'payer_name' => $requestData['payer_name'],
                'payer_email' => $requestData['payer_email'],
                'payer_id' => $requestData['payer_id'] ?? 'MISSING',
                'payer_id_type' => $requestData['payer_id_type'],
                'payer_telephone_number' => $requestData['payer_telephone_number'],
                'application_reason' => $requestData['application_reason'],
                'effective_date' => $requestData['effective_date'],
                'checksum_handling' => 'SDK_INTERNAL'
            ]);

            // Create FPX Direct Debit enrollment using SDK
            try {
                Log::info('Calling SDK createFpxDirectDebitEnrollment method', [
                    'method' => 'createFpxDirectDebitEnrollment',
                    'request_data_keys' => array_keys($requestData),
                    'has_payer_id' => isset($requestData['payer_id']),
                    'payer_id_value' => $requestData['payer_id'] ?? 'NOT_SET'
                ]);

                $response = $this->bayarcash->createFpxDirectDebitEnrollment($requestData);

                Log::info('SDK response received', [
                    'response_type' => gettype($response),
                    'response_data' => $response
                ]);

            } catch (Exception $sdkException) {
                Log::error('SDK error during e-mandate enrollment creation', [
                    'error' => $sdkException->getMessage(),
                    'error_code' => $sdkException->getCode(),
                    'error_trace' => $sdkException->getTraceAsString(),
                    'order_number' => $requestData['order_number'],
                    'request_data' => $requestData
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to create e-mandate enrollment: ' . $sdkException->getMessage(),
                    'data' => null
                ];
            }

            // Check if the response has the enrollment URL
            if (!isset($response->url) || empty($response->url)) {
                Log::error('BayarCash e-mandate enrollment URL not found', [
                    'response_class' => get_class($response),
                    'response_properties' => get_object_vars($response)
                ]);
                return [
                    'success' => false,
                    'message' => 'Failed to create e-mandate enrollment - no URL returned',
                    'data' => $response
                ];
            }

            // Store enrollment in database
            $enrollment = BayarCashEMandate::create([
                'user_id' => $data['user_id'] ?? null,
                'company_id' => $data['company_id'] ?? null,
                'order_number' => $requestData['order_number'],
                'order_description' => $requestData['order_description'],
                'portal_key' => $requestData['portal_key'],
                'amount' => $requestData['amount'],
                'payer_id_type' => $requestData['payer_id_type'],
                'payer_id' => $requestData['payer_id'],
                'payer_name' => $requestData['payer_name'],
                'payer_email' => $requestData['payer_email'],
                'payer_telephone_number' => $requestData['payer_telephone_number'],
                'frequency_mode' => $requestData['frequency_mode'],
                'application_reason' => $requestData['application_reason'],
                'effective_date' => $requestData['effective_date'],
                'expiry_date' => $requestData['expiry_date'] ?? null,
                'status' => BayarCashEMandate::STATUS_PENDING,
                'enrollment_url' => $response->url,
                'return_url' => $requestData['return_url'],
                'success_url' => $requestData['success_url'],
                'failed_url' => $requestData['failed_url'],
                'metadata' => $requestData['metadata'] ?? null
            ]);

            Log::info('E-mandate enrollment stored in database', [
                'enrollment_id' => $enrollment->id,
                'order_number' => $enrollment->order_number
            ]);

            return [
                'success' => true,
                'message' => 'E-mandate enrollment intent created successfully',
                'data' => [
                    'enrollment_id' => $enrollment->id,
                    'order_number' => $enrollment->order_number,
                    'enrollment_url' => $response->url,
                    'enrollment' => $enrollment
                ]
            ];

        } catch (Exception $e) {
            Log::error('Error creating BayarCash e-mandate enrollment intent', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while creating e-mandate enrollment: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Handle e-mandate enrollment callback
     * 
     * @param array $callbackData Callback data from BayarCash
     * @return array Result with success status and message
     */
    public function handleEnrollmentCallback(array $callbackData): array
    {
        try {
            // Comprehensive callback logging
            Log::info('=== BayarCash E-Mandate Enrollment CALLBACK Received ===', [
                'timestamp' => now()->toISOString(),
                'callback_data' => $callbackData,
                'data_keys' => array_keys($callbackData),
                'order_number' => $callbackData['order_number'] ?? 'MISSING',
                'status' => $callbackData['status'] ?? 'MISSING',
                'status_type' => gettype($callbackData['status'] ?? null),
                'transaction_id' => $callbackData['transaction_id'] ?? 'MISSING',
                'checksum' => $callbackData['checksum'] ?? 'MISSING'
            ]);

            // Analyze the status code
            $status = $callbackData['status'] ?? null;
            Log::info('BayarCash E-Mandate Callback - Status Analysis', [
                'status_raw' => $status,
                'status_interpretation' => $this->interpretCallbackStatus($status),
                'will_trigger_success_or_failure' => $this->predictStatusOutcome($status)
            ]);

            // Verify callback data using SDK's checksum verification
            Log::info('BayarCash E-Mandate Callback - Starting Checksum Verification', [
                'checksum_provided' => isset($callbackData['checksum']),
                'checksum_value' => $callbackData['checksum'] ?? 'NOT_PROVIDED',
                'api_secret_key_length' => strlen($this->apiSecretKey),
                'verification_method' => 'SDK_verifyReturnUrlCallback'
            ]);

            $isCallbackVerified = false;
            $verificationMethod = 'unknown';

            try {
                $isCallbackVerified = $this->bayarcash->verifyReturnUrlCallback(
                    $this->apiSecretKey,
                    $callbackData
                );
                $verificationMethod = 'SDK_verifyReturnUrlCallback';

                Log::info('BayarCash E-Mandate Callback - SDK Verification Result', [
                    'verification_result' => $isCallbackVerified,
                    'method_used' => $verificationMethod
                ]);

            } catch (Exception $e) {
                Log::warning('BayarCash E-Mandate Callback - SDK Verification Failed', [
                    'error' => $e->getMessage(),
                    'error_code' => $e->getCode(),
                    'attempting_fallback' => true,
                    'data' => $callbackData
                ]);

                // Fallback to custom verification
                $isCallbackVerified = $this->verifyEMandateCallback($callbackData);
                $verificationMethod = 'custom_verifyEMandateCallback';

                Log::info('BayarCash E-Mandate Callback - Custom Verification Result', [
                    'verification_result' => $isCallbackVerified,
                    'method_used' => $verificationMethod
                ]);
            }

            Log::info('BayarCash E-Mandate Callback - Final Verification Decision', [
                'is_verified' => $isCallbackVerified,
                'method_used' => $verificationMethod,
                'will_proceed' => $isCallbackVerified,
                'order_number' => $callbackData['order_number'] ?? 'MISSING'
            ]);

            if (!$isCallbackVerified) {
                Log::error('BayarCash E-Mandate Callback - VERIFICATION FAILED - Rejecting Callback', [
                    'data' => $callbackData,
                    'verification_method' => $verificationMethod,
                    'security_risk' => 'Potential unauthorized callback'
                ]);

                return [
                    'success' => false,
                    'message' => 'Callback verification failed'
                ];
            }

            $orderNumber = $callbackData['order_number'] ?? null;
            $status = $callbackData['status'] ?? null;
            $enrollmentId = $callbackData['enrollment_id'] ?? null;

            if (!$orderNumber) {
                Log::error('Order number missing in e-mandate callback', $callbackData);
                return [
                    'success' => false,
                    'message' => 'Order number missing in callback'
                ];
            }

            // Find the enrollment record
            $enrollment = BayarCashEMandate::where('order_number', $orderNumber)->first();

            if (!$enrollment) {
                Log::error('E-mandate enrollment not found', ['order_number' => $orderNumber]);
                return [
                    'success' => false,
                    'message' => 'Enrollment record not found'
                ];
            }

            // Update enrollment with callback data
            $enrollment->update([
                'enrollment_id' => $enrollmentId,
                'callback_data' => $callbackData,
                'status_code' => $callbackData['status_code'] ?? null,
                'status_message' => $callbackData['status_message'] ?? null
            ]);

            // Process status with SDK-aligned logic
            $statusInt = is_numeric($status) ? (int) $status : null;
            $sdkInterpretation = $statusInt !== null ? BayarCashEMandate::getSdkStatusInterpretation($statusInt) : null;

            Log::info('BayarCash E-Mandate Callback - Processing Status (SDK Aligned)', [
                'status_raw' => $status,
                'status_type' => gettype($status),
                'status_int' => $statusInt,
                'sdk_interpretation' => $sdkInterpretation,
                'order_number' => $orderNumber,
                'enrollment_id' => $enrollmentId,
                'current_enrollment_status' => $enrollment->status
            ]);

            // Handle numeric status codes (SDK format)
            if ($statusInt !== null) {
                switch ($statusInt) {
                    case BayarCashEMandate::SDK_STATUS_NEW: // 0
                        Log::info('BayarCash E-Mandate Callback - NEW Status Detected', [
                            'status' => $status,
                            'sdk_meaning' => 'New/Pending enrollment',
                            'action' => 'keeping_as_pending',
                            'order_number' => $orderNumber,
                            'note' => 'This is NOT a failure - enrollment is new/pending'
                        ]);

                        $enrollment->update([
                            'status' => BayarCashEMandate::STATUS_NEW,
                            'status_message' => 'E-mandate enrollment is new/pending'
                        ]);
                        break;

                    case BayarCashEMandate::SDK_STATUS_WAITING_APPROVAL: // 1
                        Log::info('BayarCash E-Mandate Callback - WAITING APPROVAL Status Detected', [
                            'status' => $status,
                            'action' => 'marking_as_waiting_approval',
                            'order_number' => $orderNumber
                        ]);

                        $enrollment->update([
                            'status' => BayarCashEMandate::STATUS_WAITING_APPROVAL,
                            'status_message' => 'E-mandate is waiting for approval'
                        ]);
                        break;

                    case BayarCashEMandate::SDK_STATUS_FAILED_BANK_VERIFICATION: // 2
                        Log::warning('BayarCash E-Mandate Callback - FAILED BANK VERIFICATION Status Detected', [
                            'status' => $status,
                            'action' => 'marking_as_failed',
                            'order_number' => $orderNumber
                        ]);

                        $enrollment->markAsFailed('Bank verification failed');
                        break;

                    case BayarCashEMandate::SDK_STATUS_ACTIVE: // 3
                        Log::info('BayarCash E-Mandate Callback - ACTIVE Status Detected', [
                            'status' => $status,
                            'action' => 'marking_as_active',
                            'order_number' => $orderNumber,
                            'enrollment_id' => $enrollmentId
                        ]);

                        $enrollment->markAsActive();
                        break;

                    case BayarCashEMandate::SDK_STATUS_TERMINATED: // 4
                        Log::warning('BayarCash E-Mandate Callback - TERMINATED Status Detected', [
                            'status' => $status,
                            'action' => 'marking_as_terminated',
                            'order_number' => $orderNumber
                        ]);

                        $enrollment->update([
                            'status' => BayarCashEMandate::STATUS_TERMINATED,
                            'status_message' => 'E-mandate has been terminated'
                        ]);
                        break;

                    case BayarCashEMandate::SDK_STATUS_APPROVED: // 5
                        Log::info('BayarCash E-Mandate Callback - APPROVED Status Detected', [
                            'status' => $status,
                            'action' => 'marking_as_approved',
                            'order_number' => $orderNumber
                        ]);

                        $enrollment->update([
                            'status' => BayarCashEMandate::STATUS_APPROVED,
                            'status_message' => 'E-mandate has been approved'
                        ]);
                        break;

                    case BayarCashEMandate::SDK_STATUS_REJECTED: // 6
                        Log::warning('BayarCash E-Mandate Callback - REJECTED Status Detected', [
                            'status' => $status,
                            'action' => 'marking_as_rejected',
                            'order_number' => $orderNumber
                        ]);

                        $enrollment->update([
                            'status' => BayarCashEMandate::STATUS_REJECTED,
                            'status_message' => 'E-mandate has been rejected'
                        ]);
                        break;

                    case BayarCashEMandate::SDK_STATUS_CANCELLED: // 7
                        Log::info('BayarCash E-Mandate Callback - CANCELLED Status Detected', [
                            'status' => $status,
                            'action' => 'marking_as_cancelled',
                            'order_number' => $orderNumber
                        ]);

                        $enrollment->markAsCancelled('E-mandate has been cancelled');
                        break;

                    case BayarCashEMandate::SDK_STATUS_ERROR: // 8
                        Log::error('BayarCash E-Mandate Callback - ERROR Status Detected', [
                            'status' => $status,
                            'action' => 'marking_as_error',
                            'order_number' => $orderNumber
                        ]);

                        $enrollment->update([
                            'status' => BayarCashEMandate::STATUS_ERROR,
                            'status_message' => 'An error occurred with the e-mandate'
                        ]);
                        break;

                    default:
                        Log::warning('BayarCash E-Mandate Callback - UNKNOWN SDK Status Code', [
                            'status' => $status,
                            'status_int' => $statusInt,
                            'action' => 'no_status_change',
                            'order_number' => $orderNumber,
                            'recommendation' => 'Check BayarCash SDK for new status codes'
                        ]);
                        break;
                }
            } else {
                // Handle string status codes (legacy format)
                switch ((string) $status) {
                    case 'completed':
                    case 'success':
                        Log::info('BayarCash E-Mandate Callback - SUCCESS (String) Status Detected', [
                            'status' => $status,
                            'action' => 'marking_as_active',
                            'order_number' => $orderNumber
                        ]);

                        $enrollment->markAsActive();
                        break;

                    case 'failed':
                        Log::warning('BayarCash E-Mandate Callback - FAILED (String) Status Detected', [
                            'status' => $status,
                            'action' => 'marking_as_failed',
                            'order_number' => $orderNumber
                        ]);

                        $enrollment->markAsFailed($callbackData['status_message'] ?? 'Enrollment failed');
                        break;

                    case 'cancelled':
                        Log::info('BayarCash E-Mandate Callback - CANCELLED (String) Status Detected', [
                            'status' => $status,
                            'action' => 'marking_as_cancelled',
                            'order_number' => $orderNumber
                        ]);

                        $enrollment->markAsCancelled($callbackData['status_message'] ?? 'Enrollment cancelled');
                        break;

                    default:
                        Log::warning('BayarCash E-Mandate Callback - UNKNOWN String Status', [
                            'status' => $status,
                            'action' => 'no_status_change',
                            'order_number' => $orderNumber
                        ]);
                        break;
                }
            }

            // Log final enrollment state
            $finalEnrollment = $enrollment->fresh();
            Log::info('BayarCash E-Mandate Callback - Final Enrollment State', [
                'order_number' => $orderNumber,
                'enrollment_id' => $finalEnrollment->id,
                'final_status' => $finalEnrollment->status,
                'status_message' => $finalEnrollment->status_message,
                'is_active' => $finalEnrollment->isActive(),
                'is_pending' => $finalEnrollment->isPending(),
                'has_failed' => $finalEnrollment->hasFailed()
            ]);

            return [
                'success' => true,
                'message' => 'Callback processed successfully',
                'data' => [
                    'enrollment' => $enrollment,
                    'status' => $status
                ]
            ];

        } catch (Exception $e) {
            Log::error('Error processing BayarCash e-mandate callback', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $callbackData
            ]);

            return [
                'success' => false,
                'message' => 'Internal server error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate enrollment data
     * 
     * @param array $data
     * @throws Exception
     */
    protected function validateEnrollmentData(array $data): void
    {
        $required = [
            'amount', 'payer_id_type', 'payer_id', 'payer_name', 
            'payer_email', 'payer_telephone_number', 'frequency_mode', 
            'application_reason', 'effective_date'
        ];

        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("Required field '{$field}' is missing or empty");
            }
        }

        // Validate amount
        if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
            throw new Exception('Amount must be a positive number');
        }

        // Validate email
        if (!filter_var($data['payer_email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email address');
        }

        // Validate frequency mode
        $validFrequencies = array_keys(BayarCashEMandate::getFrequencyModeOptions());
        if (!in_array($data['frequency_mode'], $validFrequencies)) {
            throw new Exception('Invalid frequency mode');
        }

        // Validate payer ID type
        $validIdTypes = array_keys(BayarCashEMandate::getPayerIdTypeOptions());
        if (!in_array($data['payer_id_type'], $validIdTypes)) {
            throw new Exception('Invalid payer ID type');
        }

        // Validate effective date
        try {
            $effectiveDate = Carbon::parse($data['effective_date']);
            if ($effectiveDate->isPast()) {
                throw new Exception('Effective date cannot be in the past');
            }
        } catch (Exception $e) {
            throw new Exception('Invalid effective date format');
        }
    }

    /**
     * Get enrollment by order number
     * 
     * @param string $orderNumber
     * @return BayarCashEMandate|null
     */
    public function getEnrollmentByOrderNumber(string $orderNumber): ?BayarCashEMandate
    {
        return BayarCashEMandate::where('order_number', $orderNumber)->first();
    }

    /**
     * Get active enrollments for a user
     * 
     * @param string $userId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActiveEnrollmentsForUser(string $userId)
    {
        return BayarCashEMandate::where('user_id', $userId)
            ->active()
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get enrollments due for payment
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getEnrollmentsDueForPayment()
    {
        return BayarCashEMandate::dueForPayment()
            ->orderBy('next_payment_at', 'asc')
            ->get();
    }

    /**
     * Generate checksum specifically for e-mandate enrollment
     *
     * E-mandate enrollments don't use payment_channel like regular payments,
     * so we need a custom checksum generation method that excludes this field.
     *
     * @param array $data Request data
     * @param string $apiSecretKey API secret key
     * @return string Generated checksum
     */
    protected function generateEMandateChecksum(array $data, string $apiSecretKey): string
    {
        // Define the fields required for e-mandate checksum generation
        // Based on the working payload structure, these fields must be included in this exact order
        $checksumFields = [
            'order_number',
            'order_description',
            'payer_name',
            'payer_id',
            'payer_id_type',
            'payer_telephone_number',
            'application_reason',
            'portal_key',
            'amount',
            'frequency_mode',
            'payer_email'
        ];

        // Extract only the required fields for checksum in the specified order
        $payloadData = [];
        foreach ($checksumFields as $field) {
            if (isset($data[$field])) {
                $payloadData[$field] = $data[$field];
            }
        }

        // Sort data by key (required for consistent checksum generation)
        ksort($payloadData);

        // Concatenate values with pipe delimiter
        $payloadString = implode('|', $payloadData);

        // Log the checksum generation for debugging
        Log::debug('E-mandate checksum generation', [
            'fields_used' => array_keys($payloadData),
            'payload_data' => $payloadData,
            'payload_string' => $payloadString,
            'string_length' => strlen($payloadString)
        ]);

        // Generate HMAC SHA-256 hash using the API secret key
        return hash_hmac('sha256', $payloadString, $apiSecretKey);
    }

    /**
     * Verify e-mandate callback data
     *
     * This is a basic verification method for e-mandate callbacks.
     * In production, implement proper checksum verification based on
     * BayarCash e-mandate callback documentation.
     *
     * @param array $callbackData Callback data from BayarCash
     * @return bool True if callback is valid
     */
    protected function verifyEMandateCallback(array $callbackData): bool
    {
        // Basic validation - check for required fields
        $requiredFields = ['order_number', 'status'];

        foreach ($requiredFields as $field) {
            if (!isset($callbackData[$field]) || empty($callbackData[$field])) {
                Log::warning("Required field '{$field}' missing in e-mandate callback", $callbackData);
                return false;
            }
        }

        // Additional validation can be added here
        // For now, we'll accept callbacks with required fields
        Log::info('E-mandate callback passed basic validation', [
            'order_number' => $callbackData['order_number'],
            'status' => $callbackData['status']
        ]);

        return true;
    }

    /**
     * Format Malaysian phone number to the correct format for BayarCash
     *
     * @param string $phoneNumber
     * @return string
     */
    protected function formatMalaysianPhoneNumber(string $phoneNumber): string
    {
        // Remove all non-numeric characters
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);

        // If starts with 0, replace with 60
        if (substr($cleaned, 0, 1) === '0') {
            $cleaned = '60' . substr($cleaned, 1);
        }

        // If doesn't start with 60, add 60 prefix
        if (substr($cleaned, 0, 2) !== '60') {
            $cleaned = '60' . $cleaned;
        }

        return $cleaned;
    }

    /**
     * Interpret callback status for debugging (aligned with SDK constants)
     *
     * @param mixed $status
     * @return array
     */
    private function interpretCallbackStatus($status): array
    {
        $interpretation = [
            'raw_value' => $status,
            'type' => gettype($status),
            'meaning' => 'unknown',
            'expected_action' => 'unknown'
        ];

        $statusInt = is_numeric($status) ? (int) $status : null;

        if ($statusInt !== null) {
            $sdkInterpretation = BayarCashEMandate::getSdkStatusInterpretation($statusInt);
            $interpretation = array_merge($interpretation, [
                'sdk_status_code' => $statusInt,
                'meaning' => $sdkInterpretation['status'],
                'description' => $sdkInterpretation['description'],
                'expected_action' => $this->getExpectedAction($sdkInterpretation),
                'is_success' => $sdkInterpretation['is_success'],
                'is_failure' => $sdkInterpretation['is_failure'],
                'is_pending' => $sdkInterpretation['is_pending']
            ]);
        } else {
            // Handle string status
            $statusStr = (string) $status;
            switch ($statusStr) {
                case 'success':
                case 'completed':
                    $interpretation['meaning'] = 'Success (string format)';
                    $interpretation['expected_action'] = 'Mark enrollment as active';
                    break;
                case 'failed':
                    $interpretation['meaning'] = 'Failed (string format)';
                    $interpretation['expected_action'] = 'Mark enrollment as failed';
                    break;
                case 'cancelled':
                    $interpretation['meaning'] = 'Cancelled (string format)';
                    $interpretation['expected_action'] = 'Mark enrollment as cancelled';
                    break;
                default:
                    $interpretation['meaning'] = 'Unknown string status';
                    $interpretation['expected_action'] = 'No status change';
                    break;
            }
        }

        return $interpretation;
    }

    /**
     * Get expected action based on SDK interpretation
     *
     * @param array $sdkInterpretation
     * @return string
     */
    private function getExpectedAction(array $sdkInterpretation): string
    {
        if ($sdkInterpretation['is_success']) {
            return 'Mark enrollment as active/approved';
        } elseif ($sdkInterpretation['is_failure']) {
            return 'Mark enrollment as failed/rejected/terminated';
        } elseif ($sdkInterpretation['is_pending']) {
            return 'Keep enrollment pending/new';
        } else {
            return 'No status change';
        }
    }

    /**
     * Predict the outcome based on status (aligned with SDK constants)
     *
     * @param mixed $status
     * @return string
     */
    private function predictStatusOutcome($status): string
    {
        $statusInt = is_numeric($status) ? (int) $status : null;

        if ($statusInt !== null) {
            switch ($statusInt) {
                case BayarCashEMandate::SDK_STATUS_NEW:
                    return 'NEW_PENDING';
                case BayarCashEMandate::SDK_STATUS_WAITING_APPROVAL:
                    return 'WAITING_APPROVAL';
                case BayarCashEMandate::SDK_STATUS_FAILED_BANK_VERIFICATION:
                    return 'FAILED_VERIFICATION';
                case BayarCashEMandate::SDK_STATUS_ACTIVE:
                    return 'SUCCESS_ACTIVE';
                case BayarCashEMandate::SDK_STATUS_TERMINATED:
                    return 'TERMINATED';
                case BayarCashEMandate::SDK_STATUS_APPROVED:
                    return 'APPROVED';
                case BayarCashEMandate::SDK_STATUS_REJECTED:
                    return 'REJECTED';
                case BayarCashEMandate::SDK_STATUS_CANCELLED:
                    return 'CANCELLED';
                case BayarCashEMandate::SDK_STATUS_ERROR:
                    return 'ERROR';
                default:
                    return 'UNKNOWN_SDK_STATUS';
            }
        } else {
            // Handle string status
            $statusStr = (string) $status;
            switch ($statusStr) {
                case 'success':
                case 'completed':
                    return 'SUCCESS';
                case 'failed':
                    return 'FAILURE';
                case 'cancelled':
                    return 'CANCELLED';
                default:
                    return 'UNKNOWN_STRING_STATUS';
            }
        }
    }
}
