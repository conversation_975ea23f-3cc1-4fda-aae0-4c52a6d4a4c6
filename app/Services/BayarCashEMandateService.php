<?php

namespace App\Services;

use Exception;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Models\Payment\BayarCashEMandate;
use App\Services\CustomBayarCashSDK;

/**
 * BayarCash E-Mandate Service
 * 
 * This service handles FPX Direct Debit e-mandate enrollment and management.
 * It provides methods for creating enrollment intents, handling callbacks,
 * and managing recurring payment schedules.
 */
class BayarCashEMandateService
{
    /**
     * @var CustomBayarCashSDK
     */
    protected $bayarcash;

    /**
     * @var string
     */
    protected $apiSecretKey;

    /**
     * @var bool
     */
    protected $useSandbox;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->apiSecretKey = config('services.bayarcash.api_secret_key');
        $this->useSandbox = config('services.bayarcash.sandbox', true);

        // Get base URL based on environment
        $baseUrl = $this->useSandbox
            ? config('services.bayarcash.sandbox_base_url', 'https://api.console.bayarcash-sandbox.com/v3/')
            : config('services.bayarcash.base_url', 'https://api.console.bayar.cash/v3/');

        try {
            // Initialize the custom BayarCash SDK with the correct base URL
            $this->bayarcash = new CustomBayarCashSDK(
                config('services.bayarcash.api_token'),
                $baseUrl
            );

            // Set API version
            $this->bayarcash->setApiVersion(config('services.bayarcash.api_version', 'v3'));

            // Set environment
            if ($this->useSandbox) {
                $this->bayarcash->useSandbox();
            }

            Log::info('BayarCash E-Mandate service initialized successfully');
        } catch (Exception $e) {
            Log::error('Exception when initializing BayarCash E-Mandate service', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Create an e-mandate enrollment intent
     * 
     * @param array $data Enrollment data
     * @return array Result with success status and data
     */
    public function createEnrollmentIntent(array $data): array
    {
        try {
            // Validate required fields
            $this->validateEnrollmentData($data);

            // Generate unique order number if not provided
            if (!isset($data['order_number'])) {
                $data['order_number'] = 'EMANDATE_' . uniqid() . '_' . time();
            }

            // Prepare enrollment request data
            $requestData = [
                'portal_key' => $data['portal_key'] ?? config('services.bayarcash.portal_key'),
                'order_number' => $data['order_number'],
                'amount' => $data['amount'],
                'payer_id_type' => $data['payer_id_type'],
                'payer_id' => $data['payer_id'],
                'payer_name' => $data['payer_name'],
                'payer_email' => $data['payer_email'],
                'payer_telephone_number' => $data['payer_telephone_number'],
                'frequency_mode' => $data['frequency_mode'],
                'application_reason' => $data['application_reason'],
                'effective_date' => $data['effective_date'],
                'return_url' => $data['return_url'] ?? route('payment.bayarcash-emandate.callback'),
                'success_url' => $data['success_url'] ?? route('payment.bayarcash-emandate.success'),
                'failed_url' => $data['failed_url'] ?? route('payment.bayarcash-emandate.failed'),
            ];

            // Add optional fields
            if (isset($data['expiry_date'])) {
                $requestData['expiry_date'] = $data['expiry_date'];
            }

            if (isset($data['metadata'])) {
                $requestData['metadata'] = $data['metadata'];
            }

            // Generate checksum for security (e-mandate specific)
            $checksum = $this->generateEMandateChecksum($requestData, $this->apiSecretKey);
            $requestData['checksum'] = $checksum;

            Log::info('Creating BayarCash e-mandate enrollment intent', [
                'order_number' => $requestData['order_number'],
                'portal_key' => $requestData['portal_key'],
                'amount' => $requestData['amount'],
                'frequency_mode' => $requestData['frequency_mode'],
                'payer_email' => $requestData['payer_email'],
                'checksum_generated' => substr($requestData['checksum'], 0, 10) . '...'
            ]);

            // Create FPX Direct Debit enrollment using SDK
            try {
                $response = $this->bayarcash->createFpxDirectDebitEnrollment($requestData);
            } catch (Exception $sdkException) {
                Log::error('SDK error during e-mandate enrollment creation', [
                    'error' => $sdkException->getMessage(),
                    'order_number' => $requestData['order_number']
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to create e-mandate enrollment: ' . $sdkException->getMessage(),
                    'data' => null
                ];
            }

            // Check if the response has the enrollment URL
            if (!isset($response->url) || empty($response->url)) {
                Log::error('BayarCash e-mandate enrollment URL not found', [
                    'response_class' => get_class($response),
                    'response_properties' => get_object_vars($response)
                ]);
                return [
                    'success' => false,
                    'message' => 'Failed to create e-mandate enrollment - no URL returned',
                    'data' => $response
                ];
            }

            // Store enrollment in database
            $enrollment = BayarCashEMandate::create([
                'user_id' => $data['user_id'] ?? null,
                'company_id' => $data['company_id'] ?? null,
                'order_number' => $requestData['order_number'],
                'portal_key' => $requestData['portal_key'],
                'amount' => $requestData['amount'],
                'payer_id_type' => $requestData['payer_id_type'],
                'payer_id' => $requestData['payer_id'],
                'payer_name' => $requestData['payer_name'],
                'payer_email' => $requestData['payer_email'],
                'payer_telephone_number' => $requestData['payer_telephone_number'],
                'frequency_mode' => $requestData['frequency_mode'],
                'application_reason' => $requestData['application_reason'],
                'effective_date' => $requestData['effective_date'],
                'expiry_date' => $requestData['expiry_date'] ?? null,
                'status' => BayarCashEMandate::STATUS_PENDING,
                'enrollment_url' => $response->url,
                'return_url' => $requestData['return_url'],
                'success_url' => $requestData['success_url'],
                'failed_url' => $requestData['failed_url'],
                'metadata' => $requestData['metadata'] ?? null
            ]);

            Log::info('E-mandate enrollment stored in database', [
                'enrollment_id' => $enrollment->id,
                'order_number' => $enrollment->order_number
            ]);

            return [
                'success' => true,
                'message' => 'E-mandate enrollment intent created successfully',
                'data' => [
                    'enrollment_id' => $enrollment->id,
                    'order_number' => $enrollment->order_number,
                    'enrollment_url' => $response->url,
                    'enrollment' => $enrollment
                ]
            ];

        } catch (Exception $e) {
            Log::error('Error creating BayarCash e-mandate enrollment intent', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while creating e-mandate enrollment: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Handle e-mandate enrollment callback
     * 
     * @param array $callbackData Callback data from BayarCash
     * @return array Result with success status and message
     */
    public function handleEnrollmentCallback(array $callbackData): array
    {
        try {
            Log::info('BayarCash e-mandate enrollment callback received', $callbackData);

            // Verify callback data using SDK's checksum verification
            // Note: For e-mandate callbacks, we may need custom verification if the SDK
            // expects payment_channel which is not used in e-mandate flows
            try {
                $isCallbackVerified = $this->bayarcash->verifyReturnUrlCallback(
                    $this->apiSecretKey,
                    $callbackData
                );
            } catch (Exception $e) {
                Log::warning('SDK callback verification failed, attempting custom verification', [
                    'error' => $e->getMessage(),
                    'data' => $callbackData
                ]);

                // For now, we'll proceed with basic validation
                // In production, implement custom e-mandate callback verification
                $isCallbackVerified = $this->verifyEMandateCallback($callbackData);
            }

            if (!$isCallbackVerified) {
                Log::warning('BayarCash e-mandate callback verification failed', ['data' => $callbackData]);
                return [
                    'success' => false,
                    'message' => 'Callback verification failed'
                ];
            }

            $orderNumber = $callbackData['order_number'] ?? null;
            $status = $callbackData['status'] ?? null;
            $enrollmentId = $callbackData['enrollment_id'] ?? null;

            if (!$orderNumber) {
                Log::error('Order number missing in e-mandate callback', $callbackData);
                return [
                    'success' => false,
                    'message' => 'Order number missing in callback'
                ];
            }

            // Find the enrollment record
            $enrollment = BayarCashEMandate::where('order_number', $orderNumber)->first();

            if (!$enrollment) {
                Log::error('E-mandate enrollment not found', ['order_number' => $orderNumber]);
                return [
                    'success' => false,
                    'message' => 'Enrollment record not found'
                ];
            }

            // Update enrollment with callback data
            $enrollment->update([
                'enrollment_id' => $enrollmentId,
                'callback_data' => $callbackData,
                'status_code' => $callbackData['status_code'] ?? null,
                'status_message' => $callbackData['status_message'] ?? null
            ]);

            // Process status
            switch ($status) {
                case 'completed':
                case 'success':
                    $enrollment->markAsActive();
                    Log::info("E-mandate enrollment completed successfully", [
                        'order_number' => $orderNumber,
                        'enrollment_id' => $enrollmentId
                    ]);
                    break;

                case 'failed':
                    $enrollment->markAsFailed($callbackData['status_message'] ?? 'Enrollment failed');
                    Log::warning("E-mandate enrollment failed", [
                        'order_number' => $orderNumber,
                        'message' => $callbackData['status_message'] ?? 'Unknown error'
                    ]);
                    break;

                case 'cancelled':
                    $enrollment->markAsCancelled($callbackData['status_message'] ?? 'Enrollment cancelled');
                    Log::info("E-mandate enrollment cancelled", [
                        'order_number' => $orderNumber
                    ]);
                    break;

                default:
                    Log::info("E-mandate enrollment status: {$status}", [
                        'order_number' => $orderNumber,
                        'status' => $status
                    ]);
                    break;
            }

            return [
                'success' => true,
                'message' => 'Callback processed successfully',
                'data' => [
                    'enrollment' => $enrollment,
                    'status' => $status
                ]
            ];

        } catch (Exception $e) {
            Log::error('Error processing BayarCash e-mandate callback', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $callbackData
            ]);

            return [
                'success' => false,
                'message' => 'Internal server error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate enrollment data
     * 
     * @param array $data
     * @throws Exception
     */
    protected function validateEnrollmentData(array $data): void
    {
        $required = [
            'amount', 'payer_id_type', 'payer_id', 'payer_name', 
            'payer_email', 'payer_telephone_number', 'frequency_mode', 
            'application_reason', 'effective_date'
        ];

        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("Required field '{$field}' is missing or empty");
            }
        }

        // Validate amount
        if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
            throw new Exception('Amount must be a positive number');
        }

        // Validate email
        if (!filter_var($data['payer_email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email address');
        }

        // Validate frequency mode
        $validFrequencies = array_keys(BayarCashEMandate::getFrequencyModeOptions());
        if (!in_array($data['frequency_mode'], $validFrequencies)) {
            throw new Exception('Invalid frequency mode');
        }

        // Validate payer ID type
        $validIdTypes = array_keys(BayarCashEMandate::getPayerIdTypeOptions());
        if (!in_array($data['payer_id_type'], $validIdTypes)) {
            throw new Exception('Invalid payer ID type');
        }

        // Validate effective date
        try {
            $effectiveDate = Carbon::parse($data['effective_date']);
            if ($effectiveDate->isPast()) {
                throw new Exception('Effective date cannot be in the past');
            }
        } catch (Exception $e) {
            throw new Exception('Invalid effective date format');
        }
    }

    /**
     * Get enrollment by order number
     * 
     * @param string $orderNumber
     * @return BayarCashEMandate|null
     */
    public function getEnrollmentByOrderNumber(string $orderNumber): ?BayarCashEMandate
    {
        return BayarCashEMandate::where('order_number', $orderNumber)->first();
    }

    /**
     * Get active enrollments for a user
     * 
     * @param string $userId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActiveEnrollmentsForUser(string $userId)
    {
        return BayarCashEMandate::where('user_id', $userId)
            ->active()
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get enrollments due for payment
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getEnrollmentsDueForPayment()
    {
        return BayarCashEMandate::dueForPayment()
            ->orderBy('next_payment_at', 'asc')
            ->get();
    }

    /**
     * Generate checksum specifically for e-mandate enrollment
     *
     * E-mandate enrollments don't use payment_channel like regular payments,
     * so we need a custom checksum generation method that excludes this field.
     *
     * @param array $data Request data
     * @param string $apiSecretKey API secret key
     * @return string Generated checksum
     */
    protected function generateEMandateChecksum(array $data, string $apiSecretKey): string
    {
        // Define the fields required for e-mandate checksum generation
        // Based on BayarCash e-mandate documentation, these are the core fields
        $checksumFields = [
            'portal_key',
            'order_number',
            'amount',
            'payer_name',
            'payer_email',
            'payer_id_type',
            'payer_id',
            'frequency_mode',
            'effective_date'
        ];

        // Extract only the required fields for checksum
        $payloadData = [];
        foreach ($checksumFields as $field) {
            if (isset($data[$field])) {
                $payloadData[$field] = $data[$field];
            }
        }

        // Sort data by key (required for consistent checksum generation)
        ksort($payloadData);

        // Concatenate values with pipe delimiter
        $payloadString = implode('|', $payloadData);

        // Log the checksum generation for debugging
        Log::debug('E-mandate checksum generation', [
            'fields_used' => array_keys($payloadData),
            'payload_string' => $payloadString,
            'string_length' => strlen($payloadString)
        ]);

        // Generate HMAC SHA-256 hash using the API secret key
        return hash_hmac('sha256', $payloadString, $apiSecretKey);
    }

    /**
     * Verify e-mandate callback data
     *
     * This is a basic verification method for e-mandate callbacks.
     * In production, implement proper checksum verification based on
     * BayarCash e-mandate callback documentation.
     *
     * @param array $callbackData Callback data from BayarCash
     * @return bool True if callback is valid
     */
    protected function verifyEMandateCallback(array $callbackData): bool
    {
        // Basic validation - check for required fields
        $requiredFields = ['order_number', 'status'];

        foreach ($requiredFields as $field) {
            if (!isset($callbackData[$field]) || empty($callbackData[$field])) {
                Log::warning("Required field '{$field}' missing in e-mandate callback", $callbackData);
                return false;
            }
        }

        // Additional validation can be added here
        // For now, we'll accept callbacks with required fields
        Log::info('E-mandate callback passed basic validation', [
            'order_number' => $callbackData['order_number'],
            'status' => $callbackData['status']
        ]);

        return true;
    }
}
