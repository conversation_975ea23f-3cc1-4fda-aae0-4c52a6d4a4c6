<?php

namespace App\Http\Controllers\Payment;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Payment\BayarCashEMandate;
use App\Services\BayarCashEMandateService;

/**
 * BayarCash E-Mandate Controller
 * 
 * This controller handles FPX Direct Debit e-mandate enrollment and management.
 * It provides endpoints for creating enrollment intents, handling callbacks,
 * and managing e-mandate status.
 */
class BayarCashEMandateController extends Controller
{
    /**
     * @var BayarCashEMandateService
     */
    protected $eMandateService;

    /**
     * Constructor
     */
    public function __construct(BayarCashEMandateService $eMandateService)
    {
        $this->eMandateService = $eMandateService;
    }

    /**
     * Show the e-mandate enrollment form
     * 
     * @return \Illuminate\View\View
     */
    public function showEnrollmentForm()
    {
        return view('frontend.payment.bayarcash-emandate-enrollment', [
            'frequencyOptions' => BayarCashEMandate::getFrequencyModeOptions(),
            'payerIdTypeOptions' => BayarCashEMandate::getPayerIdTypeOptions(),
            'portalKey' => config('services.bayarcash.portal_key')
        ]);
    }

    /**
     * Create an e-mandate enrollment intent
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function createEnrollmentIntent(Request $request)
    {
        try {
            // Log the incoming request for debugging
            Log::debug('BayarCash e-mandate enrollment request received', [
                'request_data' => $request->all(),
                'has_payer_id' => $request->has('payer_id'),
                'payer_id_value' => $request->input('payer_id')
            ]);

            // Validate the request
            $request->validate([
                'amount' => 'required|numeric|min:1',
                'payer_id_type' => 'required|string|in:1,2,3',
                'payer_id' => 'required|string|max:50',
                'payer_name' => 'required|string|max:255',
                'payer_email' => 'required|email|max:255',
                'payer_telephone_number' => 'required|string|regex:/^[0-9+\-\s()]+$/|max:20',
                'frequency_mode' => 'required|string|in:MT,YR',
                'application_reason' => 'required|string|max:500',
                'order_description' => 'nullable|string|max:255',
                'order_number' => 'nullable|string|max:30',
                'effective_date' => 'required|date|after:today',
                'expiry_date' => 'nullable|date|after:effective_date',
                'metadata' => 'nullable|array'
            ]);

            // Prepare enrollment data
            $data = [
                'portal_key' => config('services.bayarcash.portal_key'),
                'amount' => $request->amount,
                'payer_id_type' => $request->payer_id_type,
                'payer_id' => $request->payer_id,
                'payer_name' => $request->payer_name,
                'payer_email' => $request->payer_email,
                'payer_telephone_number' => $request->payer_telephone_number,
                'frequency_mode' => $request->frequency_mode,
                'application_reason' => $request->application_reason,
                'order_description' => $request->order_description,
                'order_number' => $request->order_number,
                'effective_date' => $request->effective_date,
                'expiry_date' => $request->expiry_date,
                'return_url' => route('payment.bayarcash-emandate.callback'),
                'success_url' => route('payment.bayarcash-emandate.success'),
                'failed_url' => route('payment.bayarcash-emandate.failed'),
                'metadata' => $request->metadata
            ];

            // Add authenticated user and company if available
            if (auth()->check()) {
                $data['user_id'] = auth()->id();
                
                if (auth()->user()->company) {
                    $data['company_id'] = auth()->user()->company->id;
                }
            }

            // Create enrollment intent
            $result = $this->eMandateService->createEnrollmentIntent($data);

            if (!$result['success']) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => $result['message']
                    ], 500);
                }

                return redirect()->back()
                    ->withErrors(['enrollment' => $result['message']])
                    ->withInput();
            }

            // Log successful enrollment creation
            Log::info('E-mandate enrollment intent created successfully', [
                'enrollment_id' => $result['data']['enrollment_id'],
                'order_number' => $result['data']['order_number'],
                'user_id' => auth()->id() ?? 'guest'
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['data']
                ]);
            }

            // Redirect to BayarCash enrollment page
            return redirect()->away($result['data']['enrollment_url']);

        } catch (Exception $e) {
            Log::error('Error creating e-mandate enrollment intent', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->withErrors(['enrollment' => 'An error occurred while creating the enrollment.'])
                ->withInput();
        }
    }

    /**
     * Handle e-mandate enrollment callback from BayarCash
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handleEnrollmentCallback(Request $request)
    {
        try {
            $callbackData = $request->all();
            
            Log::info('E-mandate enrollment callback received', [
                'order_number' => $callbackData['order_number'] ?? 'unknown',
                'status' => $callbackData['status'] ?? 'unknown'
            ]);

            // Process the callback
            $result = $this->eMandateService->handleEnrollmentCallback($callbackData);

            if (!$result['success']) {
                Log::warning('E-mandate callback processing failed', [
                    'message' => $result['message'],
                    'data' => $callbackData
                ]);

                return response()->json([
                    'status' => 'error',
                    'message' => $result['message']
                ], 400);
            }

            Log::info('E-mandate callback processed successfully', [
                'order_number' => $callbackData['order_number'] ?? 'unknown',
                'status' => $result['data']['status'] ?? 'unknown'
            ]);

            // BayarCash expects a 200 OK response to acknowledge receipt of the callback
            return response()->json([
                'status' => 'success',
                'message' => 'Callback received and processed'
            ]);

        } catch (Exception $e) {
            Log::error('Error processing e-mandate enrollment callback', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $request->all()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Handle successful e-mandate enrollment return
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function handleSuccessReturn(Request $request)
    {
        // Comprehensive logging for success return URL
        Log::info('=== BayarCash E-Mandate SUCCESS Return URL Handler ===', [
            'timestamp' => now()->toISOString(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'all_parameters' => $request->all(),
            'query_parameters' => $request->query(),
            'headers' => $request->headers->all(),
            'user_agent' => $request->userAgent(),
            'ip_address' => $request->ip()
        ]);

        $orderNumber = $request->get('order_number');
        $status = $request->get('status');
        $transactionId = $request->get('transaction_id');
        $checksum = $request->get('checksum');

        // Log key parameters analysis
        Log::info('BayarCash E-Mandate Success Return - Parameter Analysis', [
            'order_number' => $orderNumber,
            'status' => $status,
            'status_type' => gettype($status),
            'status_interpretation' => $this->interpretBayarCashStatus($status),
            'transaction_id' => $transactionId,
            'amount' => $request->get('amount'),
            'currency' => $request->get('currency'),
            'payer_bank_name' => $request->get('payer_bank_name'),
            'exchange_reference_number' => $request->get('exchange_reference_number'),
            'checksum_provided' => !empty($checksum),
            'checksum_value' => $checksum
        ]);

        $enrollment = null;
        if ($orderNumber) {
            $enrollment = $this->eMandateService->getEnrollmentByOrderNumber($orderNumber);

            Log::info('BayarCash E-Mandate Success Return - Enrollment Lookup', [
                'order_number' => $orderNumber,
                'enrollment_found' => !is_null($enrollment),
                'enrollment_id' => $enrollment?->id,
                'enrollment_status' => $enrollment?->status,
                'enrollment_status_message' => $enrollment?->status_message
            ]);
        }

        // Log the decision to show success page
        Log::info('BayarCash E-Mandate Success Return - Showing Success Page', [
            'order_number' => $orderNumber,
            'enrollment_id' => $enrollment?->id ?? 'not_found',
            'view' => 'frontend.payment.bayarcash-emandate-success'
        ]);

        return view('frontend.payment.bayarcash-emandate-success', [
            'enrollment' => $enrollment,
            'orderNumber' => $orderNumber
        ]);
    }

    /**
     * Handle failed e-mandate enrollment return
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function handleFailedReturn(Request $request)
    {
        // Comprehensive logging for failed return URL
        Log::warning('=== BayarCash E-Mandate FAILED Return URL Handler ===', [
            'timestamp' => now()->toISOString(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'all_parameters' => $request->all(),
            'query_parameters' => $request->query(),
            'headers' => $request->headers->all(),
            'user_agent' => $request->userAgent(),
            'ip_address' => $request->ip()
        ]);

        $orderNumber = $request->get('order_number');
        $status = $request->get('status');
        $transactionId = $request->get('transaction_id');
        $checksum = $request->get('checksum');
        $errorMessage = $request->get('error_message', 'E-mandate enrollment failed');

        // Log key parameters analysis
        Log::warning('BayarCash E-Mandate Failed Return - Parameter Analysis', [
            'order_number' => $orderNumber,
            'status' => $status,
            'status_type' => gettype($status),
            'status_interpretation' => $this->interpretBayarCashStatus($status),
            'transaction_id' => $transactionId,
            'amount' => $request->get('amount'),
            'currency' => $request->get('currency'),
            'payer_bank_name' => $request->get('payer_bank_name'),
            'exchange_reference_number' => $request->get('exchange_reference_number'),
            'checksum_provided' => !empty($checksum),
            'checksum_value' => $checksum,
            'error_message' => $errorMessage
        ]);

        // Critical analysis: Why are we on the failed page?
        Log::critical('BayarCash E-Mandate Failed Return - Root Cause Analysis', [
            'question' => 'Why was user redirected to failed page despite choosing success in FPX?',
            'status_code' => $status,
            'status_meaning' => $this->interpretBayarCashStatus($status),
            'possible_causes' => [
                'status_0_means_failed_or_pending',
                'bayarcash_routing_logic_issue',
                'fpx_test_environment_behavior',
                'callback_processing_error'
            ],
            'next_steps' => [
                'check_callback_logs',
                'verify_status_code_mapping',
                'check_bayarcash_documentation'
            ]
        ]);

        $enrollment = null;
        if ($orderNumber) {
            $enrollment = $this->eMandateService->getEnrollmentByOrderNumber($orderNumber);

            Log::warning('BayarCash E-Mandate Failed Return - Enrollment Lookup', [
                'order_number' => $orderNumber,
                'enrollment_found' => !is_null($enrollment),
                'enrollment_id' => $enrollment?->id,
                'enrollment_status' => $enrollment?->status,
                'enrollment_status_message' => $enrollment?->status_message,
                'enrollment_callback_data' => $enrollment?->callback_data
            ]);
        }

        // Log the decision to show failed page
        Log::warning('BayarCash E-Mandate Failed Return - Showing Failed Page', [
            'order_number' => $orderNumber,
            'enrollment_id' => $enrollment?->id ?? 'not_found',
            'error_message' => $errorMessage,
            'view' => 'frontend.payment.bayarcash-emandate-failed'
        ]);

        return view('frontend.payment.bayarcash-emandate-failed', [
            'enrollment' => $enrollment,
            'orderNumber' => $orderNumber,
            'errorMessage' => $errorMessage
        ]);
    }

    /**
     * Check e-mandate enrollment status
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkEnrollmentStatus(Request $request)
    {
        try {
            $request->validate([
                'order_number' => 'required|string'
            ]);

            $enrollment = $this->eMandateService->getEnrollmentByOrderNumber($request->order_number);

            if (!$enrollment) {
                return response()->json([
                    'success' => false,
                    'message' => 'Enrollment not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'enrollment_id' => $enrollment->id,
                    'order_number' => $enrollment->order_number,
                    'status' => $enrollment->status,
                    'status_message' => $enrollment->status_message,
                    'amount' => $enrollment->amount,
                    'frequency_mode' => $enrollment->frequency_mode,
                    'effective_date' => $enrollment->effective_date,
                    'next_payment_at' => $enrollment->next_payment_at,
                    'enrolled_at' => $enrollment->enrolled_at,
                    'is_active' => $enrollment->isActive(),
                    'is_pending' => $enrollment->isPending(),
                    'has_failed' => $enrollment->hasFailed()
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Error checking e-mandate enrollment status', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's e-mandate enrollments
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserEnrollments(Request $request)
    {
        try {
            if (!auth()->check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required'
                ], 401);
            }

            $enrollments = $this->eMandateService->getActiveEnrollmentsForUser(auth()->id());

            return response()->json([
                'success' => true,
                'data' => $enrollments->map(function ($enrollment) {
                    return [
                        'enrollment_id' => $enrollment->id,
                        'order_number' => $enrollment->order_number,
                        'status' => $enrollment->status,
                        'amount' => $enrollment->amount,
                        'frequency_mode' => $enrollment->frequency_mode,
                        'effective_date' => $enrollment->effective_date,
                        'next_payment_at' => $enrollment->next_payment_at,
                        'enrolled_at' => $enrollment->enrolled_at,
                        'application_reason' => $enrollment->application_reason
                    ];
                })
            ]);

        } catch (Exception $e) {
            Log::error('Error getting user e-mandate enrollments', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show e-mandate status checking interface
     *
     * @return \Illuminate\View\View
     */
    public function showStatusChecker()
    {
        return view('frontend.payment.bayarcash-emandate-status-checker');
    }

    /**
     * Interpret BayarCash status codes for debugging
     *
     * @param mixed $status
     * @return array
     */
    private function interpretBayarCashStatus($status): array
    {
        $interpretation = [
            'raw_value' => $status,
            'type' => gettype($status),
            'meaning' => 'unknown',
            'expected_outcome' => 'unknown',
            'documentation_reference' => 'check_bayarcash_docs'
        ];

        // Convert to string for comparison
        $statusStr = (string) $status;

        switch ($statusStr) {
            case '0':
                $interpretation['meaning'] = 'New/Pending/Failed - Status unclear in e-mandate context';
                $interpretation['expected_outcome'] = 'Should be pending or failed, not success';
                $interpretation['note'] = 'This might explain why user was redirected to failed page';
                break;
            case '1':
                $interpretation['meaning'] = 'Pending';
                $interpretation['expected_outcome'] = 'Enrollment in progress';
                break;
            case '2':
                $interpretation['meaning'] = 'Failed';
                $interpretation['expected_outcome'] = 'Enrollment failed';
                break;
            case '3':
                $interpretation['meaning'] = 'Success/Completed';
                $interpretation['expected_outcome'] = 'Enrollment successful';
                break;
            case '4':
                $interpretation['meaning'] = 'Cancelled';
                $interpretation['expected_outcome'] = 'Enrollment cancelled';
                break;
            case 'success':
            case 'completed':
                $interpretation['meaning'] = 'Success (string format)';
                $interpretation['expected_outcome'] = 'Enrollment successful';
                break;
            case 'failed':
                $interpretation['meaning'] = 'Failed (string format)';
                $interpretation['expected_outcome'] = 'Enrollment failed';
                break;
            case 'cancelled':
                $interpretation['meaning'] = 'Cancelled (string format)';
                $interpretation['expected_outcome'] = 'Enrollment cancelled';
                break;
            default:
                $interpretation['meaning'] = 'Unknown status code';
                $interpretation['expected_outcome'] = 'Undefined behavior';
                break;
        }

        return $interpretation;
    }
}
