<?php

namespace App\Http\Controllers\remote_configs;

use Illuminate\Http\Request;
use App\Models\Mobile\RemoteConfig;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;

class RemoteConfigsController extends Controller
{
    public function updateBizappXios(Request $request)
{
    try {
        $update = RemoteConfig::Where('b_product', 'bizappx')->first();

        if ($update) {
            $update->b_version_ios = $request->input('version');
            $update->release_note = $request->filled('release_note') ? $request->input('release_note') : $update->release_note;
            $update->force_ios = $request->input('force_ios');
               // Log the SQL query
               Log::info('SQL Query: ' . $update->toSql());
            $update->save();
    
            return response()->json(['message' => 'Updated Bizapp X ios version to ' . $request['version']]);
        } else {
            return response()->json(['message' => 'Record not found'], 404);
        }
    } catch (\Exception $e) {
        // Log the error
        Log::error($e->getMessage());
        return response()->json(['message' => 'An error occurred while updating the record'], 500);
    }
}


    public function updateBizappXAndroid(Request $request)
    {
        try {
            $update = RemoteConfig::Where('b_product', 'bizappx')->first();
    
            if ($update) {
                $update->b_version_android = $request->input('version');
                $update->release_note = $request->filled('release_note') ? $request->input('release_note') : $update->release_note;
                $update->force_android = $request->input('force_android');
                   // Log the SQL query
                   Log::info('SQL Query: ' . $update->toSql());
                $update->save();
        
                return response()->json(['message' => 'Updated Bizapp X android version to ' . $request['version']]);
            } else {
                return response()->json(['message' => 'Record not found'], 404);
            }
        } catch (\Exception $e) {
            // Log the error
            Log::error($e->getMessage());
            return response()->json(['message' => 'An error occurred while updating the record'], 500);
        }
    }

    public function updateBizapposIos(Request $request)
    {
        try {
         $update = RemoteConfig::Where('b_product', 'bizappos')->first();

            if ($update) {
                $update->b_version_ios = $request->input('version');
                $update->release_note = $request->filled('release_note') ? $request->input('release_note') : $update->release_note;
                $update->force_ios = $request->input('force_ios');
                // Log the SQL query
                Log::info('SQL Query: ' . $update->toSql());
                $update->save();
        
                return response()->json(['message' => 'Updated Bizappos ios version to ' . $request['version']]);
            } else {
                return response()->json(['message' => 'Record not found'], 404);
            }
        } catch (\Exception $e) {
            // Log the error
            Log::error($e->getMessage());
            return response()->json(['message' => 'An error occurred while updating the record'], 500);
        }
    }


    public function updateBizapposAndroid(Request $request)
    {
        try {
            $update = RemoteConfig::Where('b_product', 'bizappos')->first();
    
            if ($update) {
                $update->b_version_android = $request->input('version');
                $update->release_note = $request->filled('release_note') ? $request->input('release_note') : $update->release_note;
                $update->force_android = $request->input('force_android');
                   // Log the SQL query
                   Log::info('SQL Query: ' . $update->toSql());
                $update->save();
        
                return response()->json(['message' => 'Updated Bizappos android version to ' . $request['version']]);
            } else {
                return response()->json(['message' => 'Record not found'], 404);
            }
        } catch (\Exception $e) {
            // Log the error
            Log::error($e->getMessage());
            return response()->json(['message' => 'An error occurred while updating the record'], 500);
        }
    }

    public function getCurrentVersionBizappMobile(Request $request)
    {
        if($request->input('bizapp_product') == 'bizappx'){
            $data = RemoteConfig::Select(['b_version_ios', 'b_version_android','release_note','force_ios','force_android'])->Where('b_product', 'bizappx')->first();
        } else {
            $data = RemoteConfig::Select(['b_version_ios', 'b_version_android','release_note','force_ios','force_android'])->Where('b_product', 'bizappos')->first();
        }


        return response()->json($data);
    }
}
