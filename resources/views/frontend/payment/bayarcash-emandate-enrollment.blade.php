@extends('layouts.auth')

@section('title', 'BayarCash E-Mandate Enrollment')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>
                        BayarCash E-Mandate Enrollment
                    </h4>
                    <p class="mb-0 mt-2">Set up FPX Direct Debit for recurring payments</p>
                </div>
                
                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    @if (!empty($prefillData))
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Testing Mode:</strong> Form has been prefilled with data from the latest enrollment
                            @if ($latestEnrollment)
                                (Order: {{ $latestEnrollment->order_number }})
                            @endif
                            to speed up testing. You can modify any field as needed.
                        </div>
                    @endif

                    <form action="{{ route('payment.bayarcash-emandate.create') }}" method="POST" id="emandateForm">
                        @csrf
                        
                        <!-- Payment Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-money-bill-wave me-2"></i>Payment Information
                                </h5>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="amount" class="form-label">Recurring Amount (RM) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="amount" name="amount"
                                           value="{{ old('amount', $prefillData['amount'] ?? '') }}" step="0.01" min="1" required>
                                    <div class="form-text">The amount to be charged on each recurring cycle</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="frequency_mode" class="form-label">Payment Frequency <span class="text-danger">*</span></label>
                                    <select class="form-select" id="frequency_mode" name="frequency_mode" required>
                                        <option value="">Select frequency</option>
                                        @foreach($frequencyOptions as $value => $label)
                                            <option value="{{ $value }}" {{ (old('frequency_mode', $prefillData['frequency_mode'] ?? '') == $value) ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="form-text">Expiry date will be auto-calculated based on frequency</div>
                                </div>
                            </div>
                        </div>

                        <!-- Payer Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-user me-2"></i>Payer Information
                                </h5>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payer_id_type" class="form-label">ID Type <span class="text-danger">*</span></label>
                                    <select class="form-select" id="payer_id_type" name="payer_id_type" required>
                                        <option value="">Select ID type</option>
                                        @foreach($payerIdTypeOptions as $value => $label)
                                            <option value="{{ $value }}" {{ (old('payer_id_type', $prefillData['payer_id_type'] ?? '') == $value) ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payer_id" class="form-label">ID Number <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="payer_id" name="payer_id"
                                           value="{{ old('payer_id', $prefillData['payer_id'] ?? '') }}" maxlength="50" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payer_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="payer_name" name="payer_name"
                                           value="{{ old('payer_name', $prefillData['payer_name'] ?? '') }}" maxlength="255" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payer_email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="payer_email" name="payer_email"
                                           value="{{ old('payer_email', $prefillData['payer_email'] ?? '') }}" maxlength="255" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payer_telephone_number" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="payer_telephone_number" name="payer_telephone_number"
                                           value="{{ old('payer_telephone_number', $prefillData['payer_telephone_number'] ?? '') }}" maxlength="20" required>
                                </div>
                            </div>
                        </div>

                        <!-- Mandate Details -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-calendar-alt me-2"></i>Mandate Details
                                </h5>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="effective_date" class="form-label">Effective Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="effective_date" name="effective_date"
                                           value="{{ old('effective_date', $prefillData['effective_date'] ?? date('Y-m-d', strtotime('+7 days'))) }}"
                                           min="{{ date('Y-m-d', strtotime('+1 day')) }}" required>
                                    <div class="form-text">When the mandate becomes active</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="expiry_date" class="form-label">Expiry Date <span class="text-muted">(Auto-calculated)</span></label>
                                    <input type="date" class="form-control" id="expiry_date" name="expiry_date"
                                           value="{{ old('expiry_date') }}" min="{{ date('Y-m-d', strtotime('+8 days')) }}">
                                    <div class="form-text">Auto-calculated based on frequency. You can modify if needed.</div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="application_reason" class="form-label">Application Reason <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="application_reason" name="application_reason"
                                              rows="3" maxlength="500" required>{{ old('application_reason', $prefillData['application_reason'] ?? 'Subscription for Premium Service') }}</textarea>
                                    <div class="form-text">Reason for setting up this e-mandate</div>
                                </div>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Important Information</h6>
                                    <ul class="mb-0">
                                        <li>By proceeding, you authorize BayarCash to debit your bank account for the specified amount on a recurring basis.</li>
                                        <li>You can cancel this mandate at any time by contacting our support team.</li>
                                        <li>The first payment will be processed on the effective date.</li>
                                        <li>You will receive notifications before each payment is processed.</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{{ url()->previous() }}" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-arrow-left me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="submitBtn">
                                        <i class="fas fa-credit-card me-2"></i>Proceed to E-Mandate Setup
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('emandateForm');
    const submitBtn = document.getElementById('submitBtn');
    const frequencyMode = document.getElementById('frequency_mode');
    const effectiveDate = document.getElementById('effective_date');
    const expiryDate = document.getElementById('expiry_date');

    // Form validation
    form.addEventListener('submit', function(e) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    });

    // Function to calculate expiry date based on frequency
    function calculateExpiryDate() {
        const frequency = frequencyMode.value;
        const effective = new Date(effectiveDate.value);

        if (!frequency || !effectiveDate.value) {
            return;
        }

        let calculatedExpiry = new Date(effective);

        switch (frequency) {
            case 'DL': // Daily
                calculatedExpiry.setFullYear(calculatedExpiry.getFullYear() + 1); // 1 year for daily
                break;
            case 'WK': // Weekly
                calculatedExpiry.setFullYear(calculatedExpiry.getFullYear() + 1); // 1 year for weekly
                break;
            case 'MT': // Monthly
                calculatedExpiry.setFullYear(calculatedExpiry.getFullYear() + 1); // 1 year for monthly
                break;
            case 'YR': // Yearly
                calculatedExpiry.setFullYear(calculatedExpiry.getFullYear() + 1); // 1 year for yearly
                break;
            default:
                calculatedExpiry.setFullYear(calculatedExpiry.getFullYear() + 1); // Default 1 year
                break;
        }

        expiryDate.value = calculatedExpiry.toISOString().split('T')[0];

        console.log('Auto-calculated expiry date:', {
            frequency: frequency,
            effectiveDate: effectiveDate.value,
            calculatedExpiry: expiryDate.value
        });
    }

    // Auto-calculate expiry date when frequency changes
    frequencyMode.addEventListener('change', calculateExpiryDate);

    // Auto-calculate expiry date when effective date changes
    effectiveDate.addEventListener('change', function() {
        const effectiveDateValue = new Date(this.value);

        if (effectiveDateValue) {
            // Update minimum expiry date
            const minExpiryDate = new Date(effectiveDateValue);
            minExpiryDate.setDate(minExpiryDate.getDate() + 1);
            expiryDate.min = minExpiryDate.toISOString().split('T')[0];

            // Auto-calculate expiry date
            calculateExpiryDate();
        }
    });

    // Initial calculation if both fields have values (for prefilled forms)
    if (frequencyMode.value && effectiveDate.value) {
        calculateExpiryDate();
    }
});
</script>
@endsection
