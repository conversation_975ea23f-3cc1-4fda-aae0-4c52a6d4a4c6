# BayarCash E-Mandate/FPX Direct Debit Integration

This document provides comprehensive information about the BayarCash E-Mandate/FPX Direct Debit integration implemented in BizappOS.

## Overview

The BayarCash E-Mandate integration enables FPX Direct Debit functionality for recurring subscription payments. This is a separate implementation from the regular BayarCash payment integration, specifically designed for automatic recurring payments through bank direct debit.

## Key Features

- **E-Mandate Enrollment**: Set up FPX Direct Debit mandates for recurring payments using `createFpxDirectDebitEnrollment()` method
- **Multiple Frequencies**: Support for weekly, monthly, quarterly, and yearly payment cycles
- **Status Tracking**: Real-time status monitoring and callback handling
- **User Management**: User-specific enrollment management and history
- **Secure Processing**: Custom checksum validation for e-mandate (excludes payment_channel)
- **Testing Interface**: Built-in testing and status checking tools

## Architecture

### Components

1. **Model**: `BayarCashEMandate` - Handles e-mandate data and relationships
2. **Service**: `BayarCashEMandateService` - Business logic and API interactions
3. **Controller**: `BayarCashEMandateController` - HTTP request handling
4. **Views**: Frontend forms and status pages
5. **Migration**: Database schema for e-mandate tracking

### Database Schema

The `bayarcash_emandate_enrollments` table stores:
- Enrollment details (amount, frequency, dates)
- Payer information (ID, name, email, phone)
- Status tracking (pending, active, failed, cancelled, expired)
- Callback data and metadata
- Payment scheduling information

## Configuration

### Environment Variables

The integration uses the existing BayarCash configuration:

```env
BC_API_TOKEN=your_api_token
BC_API_SECRET_KEY=your_secret_key
BC_PORTAL_KEY=your_portal_key
BC_SANDBOX=true
BC_API_VERSION=v3
BC_BASE_URL=https://api.console.bayar.cash/v3/
BC_SANDBOX_BASE_URL=https://api.console.bayarcash-sandbox.com/v3/
```

### Service Configuration

Configuration is handled in `config/services.php`:

```php
'bayarcash' => [
    'api_token' => env('BC_API_TOKEN'),
    'api_secret_key' => env('BC_API_SECRET_KEY'),
    'sandbox' => env('BC_SANDBOX', true),
    'api_version' => env('BC_API_VERSION', 'v3'),
    'base_url' => env('BC_BASE_URL', 'https://api.console.bayar.cash/v3/'),
    'sandbox_base_url' => env('BC_SANDBOX_BASE_URL', 'https://api.console.bayarcash-sandbox.com/v3/'),
    'portal_key' => env('BC_PORTAL_KEY'),
]
```

## API Endpoints

### Web Routes

- `GET /payment/bayarcash-emandate/enrollment` - Show enrollment form
- `POST /payment/bayarcash-emandate/create` - Create enrollment intent
- `POST /payment/bayarcash-emandate/callback` - Handle BayarCash callbacks
- `GET /payment/bayarcash-emandate/success` - Success return page
- `GET /payment/bayarcash-emandate/failed` - Failed return page
- `GET /payment/bayarcash-emandate/status-checker` - Status checking interface
- `POST /payment/bayarcash-emandate/check-status` - Check enrollment status
- `GET /payment/bayarcash-emandate/my-enrollments` - User's enrollments

### API Routes

- `POST /api/bayarcash-emandate/create` - Create enrollment (API)
- `POST /api/bayarcash-emandate/callback` - Handle callbacks (API)
- `POST /api/bayarcash-emandate/check-status` - Check status (API)
- `GET /api/bayarcash-emandate/my-enrollments` - Get user enrollments (API)

## Usage Examples

### Creating an E-Mandate Enrollment

```php
use App\Services\BayarCashEMandateService;

$eMandateService = app(BayarCashEMandateService::class);

$data = [
    'amount' => 99.00,
    'payer_id_type' => 'NRIC',
    'payer_id' => '************',
    'payer_name' => 'John Doe',
    'payer_email' => '<EMAIL>',
    'payer_telephone_number' => '**********',
    'frequency_mode' => 'MONTHLY',
    'application_reason' => 'Subscription for Premium Service',
    'effective_date' => '2025-01-10',
    'user_id' => auth()->id(),
    'company_id' => auth()->user()->company->id
];

$result = $eMandateService->createEnrollmentIntent($data);

if ($result['success']) {
    // Redirect user to enrollment URL
    return redirect()->away($result['data']['enrollment_url']);
} else {
    // Handle error
    return back()->withErrors(['enrollment' => $result['message']]);
}
```

### Checking Enrollment Status

```php
$enrollment = $eMandateService->getEnrollmentByOrderNumber('EMANDATE_12345');

if ($enrollment) {
    echo "Status: " . $enrollment->status;
    echo "Next Payment: " . $enrollment->next_payment_at;
    echo "Is Active: " . ($enrollment->isActive() ? 'Yes' : 'No');
}
```

### Getting User's Active Enrollments

```php
$enrollments = $eMandateService->getActiveEnrollmentsForUser(auth()->id());

foreach ($enrollments as $enrollment) {
    echo "Order: {$enrollment->order_number}";
    echo "Amount: RM {$enrollment->amount}";
    echo "Frequency: {$enrollment->frequency_mode}";
}
```

## Testing

### Test Environment Setup

1. Ensure `BC_SANDBOX=true` in your `.env` file
2. Use BayarCash sandbox credentials
3. Access the enrollment form at `/payment/bayarcash-emandate/enrollment`

### Test Scenarios

1. **Successful Enrollment**:
   - Fill out the enrollment form with valid data
   - Complete the FPX process in the sandbox
   - Verify callback handling and status updates

2. **Failed Enrollment**:
   - Use invalid bank details or cancel the FPX process
   - Verify error handling and failed status

3. **Status Checking**:
   - Use the status checker at `/payment/bayarcash-emandate/status-checker`
   - Test with various order numbers

4. **API Testing**:
   - Use tools like Postman to test API endpoints
   - Verify JSON responses and error handling

### Sample Test Data

```php
// Valid test data for sandbox
$testData = [
    'amount' => 50.00,
    'payer_id_type' => 'NRIC',
    'payer_id' => '************',
    'payer_name' => 'Test User',
    'payer_email' => '<EMAIL>',
    'payer_telephone_number' => '**********',
    'frequency_mode' => 'MONTHLY',
    'application_reason' => 'Test subscription',
    'effective_date' => date('Y-m-d', strtotime('+7 days'))
];
```

## Error Handling

### Common Error Scenarios

1. **Invalid Payer Information**: Validation errors for ID, email, phone
2. **Insufficient Bank Funds**: Bank rejection during FPX process
3. **Network Issues**: API communication failures
4. **Checksum Validation**: Security validation failures
5. **Duplicate Orders**: Attempting to create duplicate enrollments

### Error Logging

All errors are logged with context:

```php
Log::error('E-mandate enrollment failed', [
    'order_number' => $orderNumber,
    'error' => $exception->getMessage(),
    'user_id' => auth()->id(),
    'trace' => $exception->getTraceAsString()
]);
```

## Security Considerations

### Checksum Validation

E-mandate enrollments use custom checksum generation that excludes `payment_channel`:

```php
// Custom e-mandate checksum generation (handled internally by the service)
$checksum = $this->generateEMandateChecksum($requestData, $apiSecretKey);
```

**Note**: E-mandate enrollments don't use `payment_channel` like regular payments, so we use a custom checksum method that includes only the relevant fields for e-mandate enrollment.

### Callback Verification

All callbacks are verified before processing:

```php
$isValid = $bayarcash->verifyReturnUrlCallback(
    $apiSecretKey,
    $callbackData
);
```

### Data Protection

- Sensitive data is encrypted in database
- API keys are stored securely in environment variables
- User data is validated and sanitized

## Monitoring and Maintenance

### Status Monitoring

Monitor e-mandate statuses regularly:

```php
// Get enrollments due for payment
$dueEnrollments = BayarCashEMandate::dueForPayment()->get();

// Get failed enrollments
$failedEnrollments = BayarCashEMandate::where('status', 'failed')->get();
```

### Cleanup Tasks

Implement cleanup for expired or cancelled enrollments:

```php
// Mark expired enrollments
BayarCashEMandate::where('expiry_date', '<', now())
    ->where('status', 'active')
    ->update(['status' => 'expired']);
```

## Troubleshooting

### Common Issues

1. **Callback Not Received**: Check firewall and URL accessibility
2. **Checksum Mismatch**: Verify API secret key configuration
3. **Enrollment Stuck in Pending**: Check BayarCash portal for status
4. **Payment Not Processing**: Verify bank account and mandate status

### Debug Mode

Enable debug logging by setting log level to debug in `config/logging.php`.

## Support

For technical support or integration issues:
- Check BayarCash documentation: https://api.webimpian.support/bayarcash/
- Contact BayarCash support team
- Review application logs for detailed error information
