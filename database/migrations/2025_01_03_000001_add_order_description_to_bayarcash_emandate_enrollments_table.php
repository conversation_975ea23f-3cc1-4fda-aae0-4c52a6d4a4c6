<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('bayarcash_emandate_enrollments', function (Blueprint $table) {
            $table->string('order_description')->nullable()->after('order_number');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('bayarcash_emandate_enrollments', function (Blueprint $table) {
            $table->dropColumn('order_description');
        });
    }
};
