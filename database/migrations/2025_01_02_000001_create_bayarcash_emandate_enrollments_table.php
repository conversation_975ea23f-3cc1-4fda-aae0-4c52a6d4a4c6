<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bayarcash_emandate_enrollments', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id')->nullable()->index();
            $table->uuid('company_id')->nullable()->index();
            
            // E-mandate enrollment details
            $table->string('enrollment_id')->unique()->nullable(); // BayarCash enrollment ID
            $table->string('order_number')->unique(); // Unique order number for enrollment
            $table->string('order_description')->nullable(); // Order description
            $table->string('portal_key'); // BayarCash portal key
            $table->decimal('amount', 10, 2); // Recurring amount
            
            // Payer information
            $table->string('payer_id_type'); // NRIC, PASSPORT, etc.
            $table->string('payer_id'); // ID number
            $table->string('payer_name');
            $table->string('payer_email');
            $table->string('payer_telephone_number');
            
            // E-mandate configuration
            $table->string('frequency_mode'); // MONTHLY, YEARLY, WEEKLY, etc.
            $table->text('application_reason'); // Reason for the mandate
            $table->date('effective_date'); // When the mandate becomes active
            $table->date('expiry_date')->nullable(); // When the mandate expires
            
            // Status tracking
            $table->string('status')->default('pending'); // pending, active, failed, cancelled, expired
            $table->string('status_code')->nullable(); // BayarCash status code
            $table->text('status_message')->nullable(); // Status description
            
            // URLs
            $table->string('enrollment_url')->nullable(); // URL to redirect user for enrollment
            $table->string('return_url')->nullable(); // Return URL after enrollment
            $table->string('success_url')->nullable(); // Success URL
            $table->string('failed_url')->nullable(); // Failed URL
            
            // Callback data
            $table->json('callback_data')->nullable(); // Store callback response
            $table->json('metadata')->nullable(); // Additional metadata
            
            // Timestamps
            $table->timestamp('enrolled_at')->nullable(); // When enrollment was completed
            $table->timestamp('last_payment_at')->nullable(); // Last successful payment
            $table->timestamp('next_payment_at')->nullable(); // Next scheduled payment
            
            $table->timestamps();
            $table->softDeletes();
            
            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('company_id')->references('id')->on('companies')->onDelete('set null');
            
            // Indexes with custom names to avoid MySQL length limit
            $table->index(['status', 'created_at'], 'bc_emandate_status_created_idx');
            $table->index(['frequency_mode', 'next_payment_at'], 'bc_emandate_freq_next_payment_idx');
            $table->index(['effective_date', 'expiry_date'], 'bc_emandate_effective_expiry_idx');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bayarcash_emandate_enrollments');
    }
};
